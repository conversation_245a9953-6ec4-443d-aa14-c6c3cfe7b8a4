# MachineHack Submission Project

## Structure
- Dataset/ (folder with unzipped train.csv, test.csv, sample_submission.csv)
- train_and_predict_cv.py
- requirements.txt

## Steps to Run
1. Ensure the Dataset folder contains `train.csv`, `test.csv`, and `sample_submission.csv`.
2. Install dependencies:
   ```
   pip install -r requirements.txt
   ```
3. Run the training & prediction script:
   ```
   python train_and_predict_cv.py
   ```
4. Upload the generated `submission.csv` to MachineHack.

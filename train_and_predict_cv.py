import pandas as pd
import numpy as np
from sklearn.model_selection import KFold
from sklearn.metrics import mean_squared_error
import lightgbm as lgb
import xgboost as xgb
import catboost as cb

# File paths (assuming unzipped dataset folder 'Dataset' in same directory)
TRAIN_PATH = 'Dataset/train.csv'
TEST_PATH = 'Dataset/test.csv'
SUB_PATH = 'Dataset/sample_submission.csv'

# Load datasets
train = pd.read_csv(TRAIN_PATH)
test = pd.read_csv(TEST_PATH)
submission = pd.read_csv(SUB_PATH)

# Separate features and target
X = train.drop(columns=['target'])
y = train['target']
X_test = test.copy()

# Prepare cross-validation
kf = KFold(n_splits=5, shuffle=True, random_state=42)

# Models
lgb_params = {'objective': 'regression', 'metric': 'rmse', 'verbose': -1}
xgb_params = {'objective': 'reg:squarederror', 'eval_metric': 'rmse', 'seed': 42}
cb_params = {'loss_function': 'RMSE', 'verbose': 0, 'random_seed': 42}

lgb_preds = np.zeros(len(X_test))
xgb_preds = np.zeros(len(X_test))
cb_preds = np.zeros(len(X_test))

cv_rmse_scores = []

for fold, (train_idx, valid_idx) in enumerate(kf.split(X)):
    print(f"Fold {fold+1}")
    X_train, X_valid = X.iloc[train_idx], X.iloc[valid_idx]
    y_train, y_valid = y.iloc[train_idx], y.iloc[valid_idx]
    
    # LightGBM
    lgb_train_data = lgb.Dataset(X_train, label=y_train)
    lgb_valid_data = lgb.Dataset(X_valid, label=y_valid)
    lgb_model = lgb.train(lgb_params, lgb_train_data, valid_sets=[lgb_valid_data], verbose_eval=False)
    lgb_preds += lgb_model.predict(X_test) / kf.n_splits
    
    # XGBoost
    xgb_model = xgb.XGBRegressor(**xgb_params)
    xgb_model.fit(X_train, y_train, eval_set=[(X_valid, y_valid)], verbose=False)
    xgb_preds += xgb_model.predict(X_test) / kf.n_splits
    
    # CatBoost
    cb_model = cb.CatBoostRegressor(**cb_params)
    cb_model.fit(X_train, y_train, eval_set=(X_valid, y_valid), verbose=False)
    cb_preds += cb_model.predict(X_test) / kf.n_splits
    
    # RMSE for fold
    val_preds = (lgb_model.predict(X_valid) + xgb_model.predict(X_valid) + cb_model.predict(X_valid)) / 3
    rmse = mean_squared_error(y_valid, val_preds, squared=False)
    cv_rmse_scores.append(rmse)
    print(f"  Fold RMSE: {rmse:.4f}")

# Average CV RMSE
print(f"Average CV RMSE: {np.mean(cv_rmse_scores):.4f}")

# Final blended prediction
final_preds = (lgb_preds + xgb_preds + cb_preds) / 3
submission['target'] = final_preds
submission.to_csv('submission.csv', index=False)
print("Saved submission.csv")
